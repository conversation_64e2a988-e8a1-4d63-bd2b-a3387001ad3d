using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Exiled.API.Features;
using MEC;

namespace PlayerBadge
{
    /// <summary>
    /// 称号管理器 - 负责加载、管理和应用玩家称号
    /// </summary>
    public class BadgeManager
    {
        /// <summary>
        /// 所有称号数据的缓存
        /// </summary>
        private List<BadgeData> _badges = new List<BadgeData>();

        /// <summary>
        /// 彩色称号的玩家列表
        /// </summary>
        private HashSet<Player> _rainbowPlayers = new HashSet<Player>();

        /// <summary>
        /// 彩色称号协程句柄
        /// </summary>
        private CoroutineHandle _rainbowCoroutine;

        /// <summary>
        /// 可用的颜色列表
        /// </summary>
        private readonly string[] _availableColors = { "red", "yellow", "cyan", "green", "aqua", "pink", "white", "orange" };

        /// <summary>
        /// 当前彩色索引
        /// </summary>
        private int _currentColorIndex = 0;

        /// <summary>
        /// 加载称号配置文件
        /// </summary>
        public void LoadBadges()
        {
            try
            {
                var configPath = PlayerBadge.Instance.Config.ConfigFilePath;
                
                // 确保目录存在
                var directory = Path.GetDirectoryName(configPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 如果文件不存在，创建示例文件
                if (!File.Exists(configPath))
                {
                    CreateExampleConfigFile(configPath);
                }

                // 读取配置文件
                var lines = File.ReadAllLines(configPath);
                _badges.Clear();

                foreach (var line in lines)
                {
                    var badge = BadgeData.ParseFromConfigLine(line);
                    if (badge != null)
                    {
                        _badges.Add(badge);
                        if (PlayerBadge.Instance.Config.Debug)
                        {
                            Log.Debug($"加载称号: {badge}");
                        }
                    }
                }

                Log.Info($"成功加载 {_badges.Count} 个玩家称号配置");

                // 启动彩色称号协程
                StartRainbowBadges();
            }
            catch (Exception ex)
            {
                Log.Error($"加载称号配置时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建示例配置文件
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        private void CreateExampleConfigFile(string configPath)
        {
            var exampleContent = @"# PlayerBadge 配置文件
# 格式: 玩家ID@平台:颜色:称号内容
# 支持的平台: steam, discord
# 支持的颜色: red, yellow, cyan, green, aqua, pink, white, orange, rainbow
# 示例:
# 76561198000000000@steam:red:管理员
# 123456789@discord:rainbow:VIP玩家
# 76561198111111111@steam:green:测试员

# 在此添加您的玩家称号配置
";
            File.WriteAllText(configPath, exampleContent);
            Log.Info($"已创建示例配置文件: {configPath}");
        }

        /// <summary>
        /// 为玩家应用称号
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void ApplyBadgeToPlayer(Player player)
        {
            if (player == null)
                return;

            try
            {
                var badge = GetBadgeForPlayer(player);
                if (badge != null)
                {
                    if (badge.IsRainbow)
                    {
                        // 彩色称号
                        _rainbowPlayers.Add(player);
                        player.RankName = badge.Text;
                        player.RankColor = _availableColors[_currentColorIndex];
                        
                        if (PlayerBadge.Instance.Config.Debug)
                        {
                            Log.Debug($"为玩家 {player.Nickname} 应用彩色称号: {badge.Text}");
                        }
                    }
                    else
                    {
                        // 单色称号
                        player.RankName = badge.Text;
                        player.RankColor = badge.Color;
                        
                        if (PlayerBadge.Instance.Config.Debug)
                        {
                            Log.Debug($"为玩家 {player.Nickname} 应用称号: {badge.Text} ({badge.Color})");
                        }
                    }

                    // 显示获取称号提示
                    if (PlayerBadge.Instance.Config.ShowBadgeHint)
                    {
                        var hintMessage = PlayerBadge.Instance.Config.BadgeHintMessage.Replace("{badge}", badge.Text);
                        player.ShowHint(hintMessage, PlayerBadge.Instance.Config.BadgeHintDuration);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"为玩家 {player?.Nickname} 应用称号时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取玩家对应的称号数据
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>称号数据，如果没有找到返回null</returns>
        private BadgeData GetBadgeForPlayer(Player player)
        {
            return _badges.FirstOrDefault(badge => badge.MatchesPlayer(player));
        }

        /// <summary>
        /// 启动彩色称号协程
        /// </summary>
        private void StartRainbowBadges()
        {
            if (_rainbowCoroutine.IsRunning)
                Timing.KillCoroutines(_rainbowCoroutine);

            _rainbowCoroutine = Timing.RunCoroutine(RainbowBadgeCoroutine());
        }

        /// <summary>
        /// 停止彩色称号协程
        /// </summary>
        public void StopRainbowBadges()
        {
            if (_rainbowCoroutine.IsRunning)
                Timing.KillCoroutines(_rainbowCoroutine);
            
            _rainbowPlayers.Clear();
        }

        /// <summary>
        /// 彩色称号协程
        /// </summary>
        /// <returns>协程迭代器</returns>
        private IEnumerator<float> RainbowBadgeCoroutine()
        {
            while (true)
            {
                yield return Timing.WaitForSeconds(PlayerBadge.Instance.Config.RainbowInterval);

                if (_rainbowPlayers.Count > 0)
                {
                    // 切换到下一个颜色
                    _currentColorIndex = (_currentColorIndex + 1) % _availableColors.Length;
                    var currentColor = _availableColors[_currentColorIndex];

                    // 为所有彩色称号玩家更新颜色
                    var playersToRemove = new List<Player>();
                    foreach (var player in _rainbowPlayers)
                    {
                        if (player == null || !player.IsConnected)
                        {
                            playersToRemove.Add(player);
                            continue;
                        }

                        try
                        {
                            player.RankColor = currentColor;
                        }
                        catch (Exception ex)
                        {
                            Log.Error($"更新玩家 {player.Nickname} 彩色称号时发生错误: {ex.Message}");
                            playersToRemove.Add(player);
                        }
                    }

                    // 清理无效的玩家引用
                    foreach (var player in playersToRemove)
                    {
                        _rainbowPlayers.Remove(player);
                    }
                }
            }
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReloadConfig()
        {
            Log.Info("正在重新加载称号配置...");
            StopRainbowBadges();
            LoadBadges();
        }

        /// <summary>
        /// 移除玩家的彩色称号状态
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void RemoveRainbowPlayer(Player player)
        {
            _rainbowPlayers.Remove(player);
        }
    }
}
