using System;

namespace PlayerBadge
{
    /// <summary>
    /// 玩家称号数据模型
    /// </summary>
    public class BadgeData
    {
        /// <summary>
        /// 玩家ID
        /// </summary>
        public string PlayerId { get; set; }

        /// <summary>
        /// 登录平台类型
        /// </summary>
        public string Platform { get; set; }

        /// <summary>
        /// 称号颜色
        /// </summary>
        public string Color { get; set; }

        /// <summary>
        /// 称号文本内容
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// 是否为彩色称号
        /// </summary>
        public bool IsRainbow { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="platform">平台类型</param>
        /// <param name="color">颜色</param>
        /// <param name="text">称号文本</param>
        public BadgeData(string playerId, string platform, string color, string text)
        {
            PlayerId = playerId;
            Platform = platform;
            Color = color;
            Text = text;
            IsRainbow = color.Equals("rainbow", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 从配置行解析称号数据
        /// 格式: 123@steam:red:你好
        /// </summary>
        /// <param name="configLine">配置行</param>
        /// <returns>解析后的称号数据，解析失败返回null</returns>
        public static BadgeData ParseFromConfigLine(string configLine)
        {
            if (string.IsNullOrWhiteSpace(configLine) || configLine.StartsWith("#"))
                return null;

            try
            {
                // 分割ID和平台部分
                var parts = configLine.Split(':');
                if (parts.Length < 3)
                    return null;

                // 解析ID@平台部分
                var idPlatformPart = parts[0];
                var atIndex = idPlatformPart.LastIndexOf('@');
                if (atIndex == -1)
                    return null;

                var playerId = idPlatformPart.Substring(0, atIndex);
                var platform = idPlatformPart.Substring(atIndex + 1);

                // 解析颜色
                var color = parts[1];

                // 解析称号文本（可能包含冒号）
                var text = string.Join(":", parts, 2, parts.Length - 2);

                return new BadgeData(playerId, platform, color, text);
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// 检查是否匹配指定的玩家
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>是否匹配</returns>
        public bool MatchesPlayer(Exiled.API.Features.Player player)
        {
            if (player == null)
                return false;

            switch (Platform.ToLower())
            {
                case "steam":
                    return player.UserId.Contains(PlayerId) || player.RawUserId.Contains(PlayerId);
                case "discord":
                    return player.UserId.Contains(PlayerId) || player.RawUserId.Contains(PlayerId);
                default:
                    // 通用匹配，检查UserId是否包含指定ID
                    return player.UserId.Contains(PlayerId) || player.RawUserId.Contains(PlayerId);
            }
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"{PlayerId}@{Platform}:{Color}:{Text}";
        }
    }
}
