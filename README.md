# PlayerBadge - SCP:SL 玩家称号插件

## 简介

PlayerBadge 是一个为 SCP: Secret Laboratory 服务器开发的 EXILED 插件，允许服务器管理员为特定玩家设置自定义称号，支持单色和彩色称号。

## 功能特性

- ✅ 支持基于玩家 Steam ID 或其他平台 ID 的称号分配
- ✅ 支持 8 种预定义颜色：red, yellow, cyan, green, aqua, pink, white, orange
- ✅ 支持彩色称号（rainbow），每 0.6 秒自动切换颜色
- ✅ 自动创建示例配置文件
- ✅ 支持配置文件热重载
- ✅ 玩家获得称号时的提示消息
- ✅ 详细的调试日志支持

## 安装方法

1. 将编译后的 `PlayerBadge.dll` 文件放入服务器的 `EXILED/Plugins` 目录
2. 重启服务器或使用 `reload plugins` 命令重载插件
3. 插件会自动在 `%appdata%\EXILED\Configs\` 目录下创建 `PlayerBadge.txt` 配置文件

## 配置文件格式

配置文件位置：`%appdata%\EXILED\Configs\PlayerBadge.txt`

### 格式说明
```
玩家ID@平台:颜色:称号内容
```

### 示例配置
```
# PlayerBadge 配置文件
# 格式: 玩家ID@平台:颜色:称号内容
# 支持的平台: steam, discord
# 支持的颜色: red, yellow, cyan, green, aqua, pink, white, orange, rainbow

# 单色称号示例
76561198000000000@steam:red:管理员
76561198111111111@steam:green:版主
123456789@discord:yellow:VIP玩家

# 彩色称号示例（每0.6秒切换颜色）
76561198222222222@steam:rainbow:超级VIP
76561198333333333@steam:rainbow:赞助者
```

### 参数说明

- **玩家ID**: 玩家的 Steam ID 或其他平台 ID
- **平台**: 目前支持 `steam` 和 `discord`
- **颜色**: 
  - 单色：`red`, `yellow`, `cyan`, `green`, `aqua`, `pink`, `white`, `orange`
  - 彩色：`rainbow`（自动在所有颜色间循环）
- **称号内容**: 显示的称号文本，支持中文和特殊字符

## 插件配置

插件的主要配置选项（在 EXILED 配置文件中）：

```yaml
player_badge:
  is_enabled: true
  debug: false
  config_file_path: "%appdata%\\EXILED\\Configs\\PlayerBadge.txt"
  rainbow_interval: 0.6
  show_badge_hint: true
  badge_hint_message: "<color=yellow>你获得了专属称号：{badge}</color>"
  badge_hint_duration: 3.0
```

### 配置选项说明

- `is_enabled`: 是否启用插件
- `debug`: 是否启用调试日志
- `config_file_path`: 称号配置文件路径
- `rainbow_interval`: 彩色称号颜色切换间隔（秒）
- `show_badge_hint`: 是否显示称号获取提示
- `badge_hint_message`: 称号获取提示消息模板
- `badge_hint_duration`: 提示消息显示时长（秒）

## 使用方法

1. 获取玩家的 Steam ID（可以通过游戏内命令或第三方工具获取）
2. 在 `PlayerBadge.txt` 文件中添加对应的配置行
3. 使用服务器命令重载插件配置（如果支持）或重启服务器
4. 玩家重新连接服务器后即可看到称号

## 命令支持

插件支持以下服务器命令（需要管理员权限）：

- `playerbadge reload` - 重新加载称号配置文件

## 技术要求

- SCP: Secret Laboratory 服务器
- EXILED 框架 9.7.1 或更高版本
- .NET Framework 4.8.1

## 故障排除

### 常见问题

1. **称号不显示**
   - 检查玩家 ID 是否正确
   - 确认配置文件格式是否正确
   - 查看服务器日志中的错误信息

2. **彩色称号不工作**
   - 确认颜色设置为 `rainbow`
   - 检查 `rainbow_interval` 配置是否合理

3. **配置文件不生效**
   - 确认文件路径是否正确
   - 检查文件编码是否为 UTF-8
   - 尝试重启服务器

### 调试模式

启用调试模式可以获得更详细的日志信息：

```yaml
player_badge:
  debug: true
```

## 开发信息

- 版本：1.0.0
- 开发框架：EXILED API
- 编程语言：C#
- 许可证：MIT

## 更新日志

### v1.0.0
- 初始版本发布
- 支持单色和彩色称号
- 自动配置文件生成
- 玩家提示消息系统
