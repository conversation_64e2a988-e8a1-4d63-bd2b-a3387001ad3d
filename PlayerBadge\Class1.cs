using System;
using Exiled.API.Features;
using Exiled.API.Interfaces;

namespace PlayerBadge
{
    /// <summary>
    /// PlayerBadge插件主类 - 为玩家提供自定义称号系统
    /// </summary>
    public class PlayerBadge : Plugin<Config>
    {
        /// <summary>
        /// 插件单例实例
        /// </summary>
        public static PlayerBadge Instance { get; private set; }

        /// <summary>
        /// 事件处理器实例
        /// </summary>
        public EventHandlers EventHandlers { get; private set; }

        /// <summary>
        /// 称号管理器实例
        /// </summary>
        public BadgeManager BadgeManager { get; private set; }

        /// <summary>
        /// 插件名称
        /// </summary>
        public override string Name => "PlayerBadge";

        /// <summary>
        /// 插件作者
        /// </summary>
        public override string Author => "EXILED Community";

        /// <summary>
        /// 插件版本
        /// </summary>
        public override Version Version => new Version(1, 0, 0);

        /// <summary>
        /// 插件启用时调用
        /// </summary>
        public override void OnEnabled()
        {
            Instance = this;

            // 初始化称号管理器
            BadgeManager = new BadgeManager();

            // 初始化事件处理器
            EventHandlers = new EventHandlers();

            // 注册事件
            Exiled.Events.Handlers.Player.Verified += EventHandlers.OnPlayerVerified;
            Exiled.Events.Handlers.Player.Left += EventHandlers.OnPlayerLeft;

            // 加载称号配置
            BadgeManager.LoadBadges();

            Log.Info("PlayerBadge插件已启用！");
            base.OnEnabled();
        }

        /// <summary>
        /// 插件禁用时调用
        /// </summary>
        public override void OnDisabled()
        {
            // 注销事件
            Exiled.Events.Handlers.Player.Verified -= EventHandlers.OnPlayerVerified;
            Exiled.Events.Handlers.Player.Left -= EventHandlers.OnPlayerLeft;

            // 停止彩色称号协程
            BadgeManager?.StopRainbowBadges();

            // 清理资源
            EventHandlers = null;
            BadgeManager = null;
            Instance = null;

            Log.Info("PlayerBadge插件已禁用！");
            base.OnDisabled();
        }
    }
}
