<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DD0BF4BC-7AC3-48E3-A5C0-A397D2DDD434}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>PlayerBadge</RootNamespace>
    <AssemblyName>PlayerBadge</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Assembly-CSharp">
      <HintPath>F:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\Assembly-CSharp.dll</HintPath>
    </Reference>
    <Reference Include="Assembly-CSharp-firstpass">
      <HintPath>F:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\Assembly-CSharp-firstpass.dll</HintPath>
    </Reference>
    <Reference Include="Assembly-CSharp-Publicized, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\Assembly-CSharp-Publicized.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="CommandSystem.Core, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\CommandSystem.Core.dll</HintPath>
    </Reference>
    <Reference Include="Exiled.API, Version=9.7.1.0, Culture=neutral, processorArchitecture=AMD64">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\Exiled.API.dll</HintPath>
    </Reference>
    <Reference Include="Exiled.CreditTags, Version=9.7.1.0, Culture=neutral, processorArchitecture=AMD64">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\Exiled.CreditTags.dll</HintPath>
    </Reference>
    <Reference Include="Exiled.CustomItems, Version=9.7.1.0, Culture=neutral, processorArchitecture=AMD64">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\Exiled.CustomItems.dll</HintPath>
    </Reference>
    <Reference Include="Exiled.CustomRoles, Version=9.7.1.0, Culture=neutral, processorArchitecture=AMD64">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\Exiled.CustomRoles.dll</HintPath>
    </Reference>
    <Reference Include="Exiled.Events, Version=9.7.1.0, Culture=neutral, processorArchitecture=AMD64">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\Exiled.Events.dll</HintPath>
    </Reference>
    <Reference Include="Exiled.Loader, Version=9.7.1.0, Culture=neutral, processorArchitecture=AMD64">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\Exiled.Loader.dll</HintPath>
    </Reference>
    <Reference Include="Exiled.Permissions, Version=9.7.1.0, Culture=neutral, processorArchitecture=AMD64">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\Exiled.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="LabApi, Version=1.1.1.0, Culture=neutral, processorArchitecture=AMD64">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\LabApi.dll</HintPath>
    </Reference>
    <Reference Include="NorthwoodLib, Version=1.4.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\NorthwoodLib.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>F:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="YamlDotNet, Version=11.0.0.0, Culture=neutral, PublicKeyToken=ec19458f3c15af5e, processorArchitecture=MSIL">
      <HintPath>..\packages\ExMod.Exiled.9.7.1\lib\net48\YamlDotNet.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Class1.cs" />
    <Compile Include="Config.cs" />
    <Compile Include="BadgeData.cs" />
    <Compile Include="BadgeManager.cs" />
    <Compile Include="EventHandlers.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>