using Exiled.API.Features;
using Exiled.Events.EventArgs.Player;

namespace PlayerBadge
{
    /// <summary>
    /// 事件处理器类 - 处理玩家相关事件
    /// </summary>
    public class EventHandlers
    {
        /// <summary>
        /// 处理玩家验证完成事件
        /// </summary>
        /// <param name="ev">玩家验证事件参数</param>
        public void OnPlayerVerified(VerifiedEventArgs ev)
        {
            try
            {
                if (ev.Player == null)
                    return;

                if (PlayerBadge.Instance.Config.Debug)
                {
                    Log.Debug($"玩家 {ev.Player.Nickname} ({ev.Player.UserId}) 已验证，正在检查称号配置...");
                }

                // 为玩家应用称号
                PlayerBadge.Instance.BadgeManager.ApplyBadgeToPlayer(ev.Player);
            }
            catch (System.Exception ex)
            {
                Log.Error($"处理玩家验证事件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理玩家离开事件
        /// </summary>
        /// <param name="ev">玩家离开事件参数</param>
        public void OnPlayerLeft(LeftEventArgs ev)
        {
            try
            {
                if (ev.Player == null)
                    return;

                // 从彩色称号列表中移除玩家
                PlayerBadge.Instance.BadgeManager?.RemoveRainbowPlayer(ev.Player);

                if (PlayerBadge.Instance.Config.Debug)
                {
                    Log.Debug($"玩家 {ev.Player.Nickname} 已离开，已清理相关数据");
                }
            }
            catch (System.Exception ex)
            {
                Log.Error($"处理玩家离开事件时发生错误: {ex.Message}");
            }
        }
    }
}
