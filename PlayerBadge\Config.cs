using System.ComponentModel;
using Exiled.API.Interfaces;

namespace PlayerBadge
{
    /// <summary>
    /// PlayerBadge插件配置类
    /// </summary>
    public class Config : IConfig
    {
        /// <summary>
        /// 插件是否启用
        /// </summary>
        [Description("是否启用PlayerBadge插件")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否启用调试模式
        /// </summary>
        [Description("是否启用调试日志输出")]
        public bool Debug { get; set; } = false;

        /// <summary>
        /// 配置文件路径
        /// </summary>
        [Description("称号配置文件的完整路径")]
        public string ConfigFilePath { get; set; } = System.IO.Path.Combine(
            System.Environment.GetFolderPath(System.Environment.SpecialFolder.ApplicationData),
            "EXILED", "Configs", "PlayerBadge.txt");

        /// <summary>
        /// 彩色称号颜色切换间隔（秒）
        /// </summary>
        [Description("彩色称号颜色切换间隔时间（秒）")]
        public float RainbowInterval { get; set; } = 0.6f;

        /// <summary>
        /// 是否在玩家加入时显示称号获取提示
        /// </summary>
        [Description("是否在玩家获得称号时显示提示消息")]
        public bool ShowBadgeHint { get; set; } = true;

        /// <summary>
        /// 称号获取提示消息
        /// </summary>
        [Description("玩家获得称号时显示的提示消息")]
        public string BadgeHintMessage { get; set; } = "<color=yellow>你获得了专属称号：{badge}</color>";

        /// <summary>
        /// 称号获取提示显示时长（秒）
        /// </summary>
        [Description("称号获取提示消息显示时长（秒）")]
        public float BadgeHintDuration { get; set; } = 3.0f;
    }
}
